name: build

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

permissions:
  id-token: write
  contents: write
  actions: read
  pull-requests: write
  issues: write

jobs:
  check-branch-name:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    steps:
      - name: Check Branch Name
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const branchName = context.payload.pull_request.head.ref;
            const regex = /^(TWC|TM|TB|PO|DEV|VER)-\d+-[\w-]+$/;

            if (!regex.test(branchName)) {
              core.setFailed('Branch name does not match the required format: TWC/TM/TB/PO/DEV/VER-<NUMBER>-<desc>');
            } else {
              console.log('Branch name matches the required format');
            }

      - name: Notify MS Teams on Branch Name Success
        if: success()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "✅ Branch name check passed!\n- Workflow: check-branch-name\n- Project:Check Branch Name\n- Branch: ${{ github.head_ref }}\n- Triggered by: ${{ github.actor }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

      - name: Notify MS Teams on Branch Name Failure
        if: failure()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "❌ Branch name check failed!\n- Workflow: check-branch-name\n- Project:Check Branch Name\n- Branch: ${{ github.head_ref }}\n- Triggered by: ${{ github.actor }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

  build-and-test:
    runs-on: ubuntu-latest
    needs: check-branch-name
    if: always() && (needs.check-branch-name.result == 'success' || needs.check-branch-name.result == 'skipped')
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Restore test projects dependencies
        shell: pwsh
        run: |
          $testProjects = Get-ChildItem -Path . -Recurse -Include *.csproj | Where-Object { $_.FullName -match 'Test' }
          if ($testProjects.Count -eq 0) {
            Write-Host "No test projects found."
            exit 1
          }
          foreach ($proj in $testProjects) {
            Write-Host "Restoring $($proj.FullName)"
            dotnet restore $proj.FullName
          }

      - name: Discover and run all test projects
        shell: pwsh
        run: |
          $testProjects = Get-ChildItem -Path . -Recurse -Include *.csproj | Where-Object { $_.FullName -match 'Test' }
          foreach ($proj in $testProjects) {
            Write-Host "Running tests in $($proj.FullName)"
            dotnet test $proj.FullName --no-restore --verbosity normal
          }

      - name: Notify MS Teams on Test Success
        if: success()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "✅ All unit tests passed!\n- Workflow: build-and-test\n- Project:Build and Test\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}\n- Triggered by: ${{ github.actor }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

      - name: Notify MS Teams on Test Failure
        if: failure()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "❌ Unit tests failed!\n- Workflow: build-and-test\n- Project:Build and Test\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}\n- Triggered by: ${{ github.actor }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}
  versioning:
    runs-on: ubuntu-latest
    needs: build-and-test
    # Run versioning automatically when:
    # 1. Build-and-test succeeds AND
    # 2. Push to main branch (automatic versioning only)
    if: success() && (needs.build-and-test.result == 'success')
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Check for changes in project files
        id: changes
        run: |
          # Get the list of changed files in the last commit
          CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD)
          echo "Changed files: $CHANGED_FILES"

          # Check if any .csproj files or source code files were changed
          CSPROJ_CHANGED=$(echo "$CHANGED_FILES" | grep -E '\.(csproj|cs|razor|js|css|html|ts|json)$' || true)

          if [ -n "$CSPROJ_CHANGED" ]; then
            echo "has_changes=true" >> $GITHUB_OUTPUT
            echo "Project-related files were changed, versioning needed"
            echo "Changed project files: $CSPROJ_CHANGED"
          else
            echo "has_changes=false" >> $GITHUB_OUTPUT
            echo "No project-related files changed, skipping versioning"
          fi

      - name: Get version and ticket number
        id: version
        if: steps.changes.outputs.has_changes == 'true'
        run: |
          # Generate version in format YYYYMMDD.HHMM-{actor}
          VERSION="$(date +'%Y%m%d.%H%M')-${{ github.actor }}"
          # Generate a ticket number for branch naming convention
          TICKET_NUMBER=$(date +'%Y%m%d%H%M')
          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT
          echo "TICKET_NUMBER=$TICKET_NUMBER" >> $GITHUB_OUTPUT
          echo "Generated version: $VERSION"
          echo "Generated ticket number: $TICKET_NUMBER"

      - name: Detect changed projects
        id: detect_projects
        if: steps.changes.outputs.has_changes == 'true'
        run: |
          # Get changed files from the last commit
          CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD)

          # Find .csproj files that were directly changed or are in directories with changed files
          CHANGED_PROJECTS=""

          # Check each changed file and find related .csproj files
          for file in $CHANGED_FILES; do
            # Get the directory of the changed file
            DIR=$(dirname "$file")

            # Look for .csproj files in the same directory or parent directories
            while [ "$DIR" != "." ] && [ "$DIR" != "/" ]; do
              CSPROJ_IN_DIR=$(find "$DIR" -maxdepth 1 -name "*.csproj" 2>/dev/null || true)
              if [ -n "$CSPROJ_IN_DIR" ]; then
                CHANGED_PROJECTS="$CHANGED_PROJECTS $CSPROJ_IN_DIR"
                break
              fi
              DIR=$(dirname "$DIR")
            done
          done

          # Remove duplicates and sort
          CHANGED_PROJECTS=$(echo "$CHANGED_PROJECTS" | tr ' ' '\n' | sort -u | tr '\n' ' ')

          if [ -z "$CHANGED_PROJECTS" ]; then
            # If no specific projects found, version all main projects
            CHANGED_PROJECTS=$(find . -name "*.csproj" -path "*/Services/*" -o -path "*/Clients/*" -o -path "*/Infrastructure/*" | grep -v Test | head -10)
          fi

          echo "projects=$CHANGED_PROJECTS" >> $GITHUB_OUTPUT
          echo "Projects to version: $CHANGED_PROJECTS"

      - name: Update version in changed projects
        if: steps.changes.outputs.has_changes == 'true'
        run: |
          VERSION=${{ steps.version.outputs.VERSION }}
          PROJECTS="${{ steps.detect_projects.outputs.projects }}"

          echo "Setting version $VERSION in changed .csproj files..."

          for csproj in $PROJECTS; do
            if [ -f "$csproj" ]; then
              echo "Updating $csproj"

              # Check if Version element exists
              if grep -q "<Version>" "$csproj"; then
                # Update existing Version element
                sed -i "s|<Version>.*</Version>|<Version>$VERSION</Version>|" "$csproj"
              else
                # Add Version element to the first PropertyGroup
                sed -i "0,/<\/PropertyGroup>/s|<\/PropertyGroup>|    <Version>$VERSION</Version>\n  </PropertyGroup>|" "$csproj"
              fi

              echo "Updated $csproj with version $VERSION"
            else
              echo "Warning: $csproj not found"
            fi
          done

      - name: Create Pull Request for Version Bump
        if: steps.changes.outputs.has_changes == 'true'
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "ci: bump version to ${{ steps.version.outputs.VERSION }}"
          branch: "VER-${{ steps.version.outputs.TICKET_NUMBER }}-version-bump"
          base: main
          title: "VER Version bump to ${{ steps.version.outputs.VERSION }}"
          body: |
            ## Automated Version Bump

            This PR contains an automated version bump to `${{ steps.version.outputs.VERSION }}`.

            ### Changes Made:
            - Updated version in modified project files
            - Version format: `YYYYMMDD.HHMM-{actor}`

            ### Projects Updated:
            ```
            ${{ steps.detect_projects.outputs.projects }}
            ```

            ### Triggered By:
            - **Actor**: ${{ github.actor }}
            - **Commit**: ${{ github.sha }}
            - **Branch**: ${{ github.ref_name }}

            ---

            ⚠️ **Note**: This is an automated PR created by the versioning workflow. Please review the changes before merging.
          labels: |
            automated
            version-bump
            ci/cd
          assignees: ${{ github.actor }}

      - name: Notify MS Teams on Versioning Success
        if: steps.changes.outputs.has_changes == 'true'
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "✅ Versioning PR created!\n- Workflow: versioning\n- Version: ${{ steps.version.outputs.VERSION }}\n- Branch: VER-${{ steps.version.outputs.TICKET_NUMBER }}-version-bump\n- Projects: ${{ steps.detect_projects.outputs.projects }}\n- Triggered by: ${{ github.actor }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

      - name: Notify MS Teams on No Changes
        if: steps.changes.outputs.has_changes == 'false'
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "ℹ️ Versioning skipped - no project changes detected\n- Workflow: versioning\n- Branch: ${{ github.ref_name }}\n- Triggered by: ${{ github.actor }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}


  cache-dependencies:
    runs-on: ubuntu-latest
    steps:
      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-docker-${{ hashFiles('**/Dockerfile') }}
          restore-keys: |
            ${{ runner.os }}-docker-

  build:
    runs-on: ubuntu-latest
    needs: [cache-dependencies, build-and-test]
    if: success()
    strategy:
      matrix:
        include:
          - name: MemberService API
            CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
            DOCKER_FILE_PATH: Services/MemberServiceApi/Dockerfile
            PROJECT_NAME_FOR_DOCKER: memberservice
            REGISTRY_USERNAME: TeyaHealthDev
          - name: EncounterNotesService API
            CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
            DOCKER_FILE_PATH: Services/EncounterNotesApi/Dockerfile
            PROJECT_NAME_FOR_DOCKER: encounternotes
            REGISTRY_USERNAME: TeyaHealthDev
          - name: Practice API
            CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
            DOCKER_FILE_PATH: Services/PracticeApi/Dockerfile
            PROJECT_NAME_FOR_DOCKER: practice
            REGISTRY_USERNAME: TeyaHealthDev
          - name: Appointments API
            CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
            DOCKER_FILE_PATH: Services/AppointmentsApi/Dockerfile
            PROJECT_NAME_FOR_DOCKER: appointments
            REGISTRY_USERNAME: TeyaHealthDev
          - name: TeyaWebApp
            CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
            DOCKER_FILE_PATH: Clients/TeyaWebClient/TeyaWebApp/TeyaWebApp/Dockerfile
            PROJECT_NAME_FOR_DOCKER: teyawebapp
            REGISTRY_USERNAME: TeyaHealthDev

    name: Build ${{ matrix.name }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ matrix.CONTAINER_REGISTRY_LOGIN_SERVER }}
          username: ${{ matrix.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}

      - name: Build container image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: false
          tags: ${{ matrix.CONTAINER_REGISTRY_LOGIN_SERVER }}/${{ matrix.PROJECT_NAME_FOR_DOCKER }}:${{ github.sha }}
          file: ${{ matrix.DOCKER_FILE_PATH }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new

      - name: Move cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      - name: Notify MS Teams on Success
        if: success()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "✅ Build succeeded!\n- Workflow: build\n- Project: ${{ matrix.name }}\n- Triggered by: ${{ github.actor }}\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

      - name: Notify MS Teams on Failure
        if: failure()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "❌ Build failed!\n- Workflow: build\n- Project: ${{ matrix.name }}\n- Triggered by: ${{ github.actor }}\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

  build-sql-database:
    runs-on: ubuntu-latest
    name: Build SQL Database
    needs: build-and-test
    if: success()
    steps:
      - uses: actions/checkout@v4

      - name: Setup .NET SDK
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '6.0.x'

      - name: Build Database Project
        run: dotnet build Database/TeyaHealthSQLDatabase/TeyaHealthSQLDatabase.sqlproj -c Debug

      - name: Install SQLPackage
        run: |
          curl -L https://aka.ms/sqlpackage-linux -o sqlpackage.zip
          sudo mkdir -p /usr/local/sqlpackage
          sudo unzip -o sqlpackage.zip -d /usr/local/sqlpackage
          sudo chmod +x /usr/local/sqlpackage/sqlpackage
          echo "/usr/local/sqlpackage" >> $GITHUB_PATH

      - name: Notify MS Teams on DB Deploy Success
        if: success()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "✅ Azure SQL Database deployment succeeded!\n- Workflow: Build Database Project\n- Project: sql-database-build\n- Triggered by: ${{ github.actor }}\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

      - name: Notify MS Teams on DB Deploy Failure
        if: failure()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "❌ Azure SQL Database deployment failed!\n- Workflow: Build Database Project\n- Project: sql-database-build\n- Triggered by: ${{ github.actor }}\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}



